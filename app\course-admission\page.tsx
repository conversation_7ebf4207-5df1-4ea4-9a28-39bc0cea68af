"use client";

import React, { useState } from "react";
import { Button } from "primereact/button";
import { InputText } from "primereact/inputtext";
import { InputTextarea } from "primereact/inputtextarea";
import { Card } from "primereact/card";
import { Dropdown } from "primereact/dropdown";
import { Calendar } from "primereact/calendar";
import { Checkbox } from "primereact/checkbox";
import { useRouter } from "next/navigation";
import Image from "next/image";
import Back from "../../public/Svg/back_icon.svg";

interface AdmissionApplication {
  personalInfo: {
    fullName: string;
    email: string;
    phone: string;
    dateOfBirth: Date | null;
    address: string;
  };
  academicInfo: {
    previousEducation: string;
    gpa: string;
    graduationYear: string;
  };
  coursePreferences: {
    primaryChoice: string;
    secondaryChoice: string;
    startDate: Date | null;
  };
  documents: {
    transcript: boolean;
    recommendation: boolean;
    essay: boolean;
  };
  essay: string;
}

const courseOptions = [
  { label: "HVAC Technology", value: "hvac" },
  { label: "Electrical Technology", value: "electrical" },
  { label: "Plumbing Technology", value: "plumbing" },
  { label: "Automotive Technology", value: "automotive" },
  { label: "Welding Technology", value: "welding" },
  { label: "Computer Information Systems", value: "cis" },
  { label: "Business Administration", value: "business" },
  { label: "Healthcare Administration", value: "healthcare" },
];

const educationLevels = [
  { label: "High School Diploma", value: "high_school" },
  { label: "GED", value: "ged" },
  { label: "Some College", value: "some_college" },
  { label: "Associate Degree", value: "associate" },
  { label: "Bachelor's Degree", value: "bachelor" },
  { label: "Master's Degree", value: "master" },
];

export default function CourseAdmission() {
  const router = useRouter();
  const [application, setApplication] = useState<AdmissionApplication>({
    personalInfo: {
      fullName: "",
      email: "",
      phone: "",
      dateOfBirth: null,
      address: "",
    },
    academicInfo: {
      previousEducation: "",
      gpa: "",
      graduationYear: "",
    },
    coursePreferences: {
      primaryChoice: "",
      secondaryChoice: "",
      startDate: null,
    },
    documents: {
      transcript: false,
      recommendation: false,
      essay: false,
    },
    essay: "",
  });

  const handlePersonalInfoChange = (field: string, value: any) => {
    setApplication(prev => ({
      ...prev,
      personalInfo: {
        ...prev.personalInfo,
        [field]: value,
      },
    }));
  };

  const handleAcademicInfoChange = (field: string, value: string) => {
    setApplication(prev => ({
      ...prev,
      academicInfo: {
        ...prev.academicInfo,
        [field]: value,
      },
    }));
  };

  const handleCoursePreferenceChange = (field: string, value: any) => {
    setApplication(prev => ({
      ...prev,
      coursePreferences: {
        ...prev.coursePreferences,
        [field]: value,
      },
    }));
  };

  const handleDocumentChange = (field: string, checked: boolean) => {
    setApplication(prev => ({
      ...prev,
      documents: {
        ...prev.documents,
        [field]: checked,
      },
    }));
  };

  const submitApplication = async () => {
    try {
      // Here you would typically send the application to your backend
      console.log("Submitting application:", application);
      
      // For now, just show a success message
      alert("Application submitted successfully! You will receive a confirmation email shortly.");
      
      // Optionally redirect back to the main page
      router.push("/");
    } catch (error) {
      console.error("Error submitting application:", error);
      alert("There was an error submitting your application. Please try again.");
    }
  };

  const isFormValid = () => {
    return (
      application.personalInfo.fullName &&
      application.personalInfo.email &&
      application.personalInfo.phone &&
      application.coursePreferences.primaryChoice &&
      application.academicInfo.previousEducation
    );
  };

  return (
    <div className="p-4">
      {/* Header */}
      <div className="flex align-items-center gap-3 mb-4">
        <Button
          icon={<Image src={Back} alt="back" width={20} height={20} />}
          className="p-button-text"
          onClick={() => router.back()}
        />
        <h1 className="text-3xl font-bold m-0">Course Admission Application</h1>
      </div>

      <div className="grid">
        <div className="col-12 lg:col-8">
          {/* Personal Information */}
          <Card title="Personal Information" className="mb-4">
            <div className="grid">
              <div className="col-12 md:col-6">
                <label htmlFor="fullName" className="block text-900 font-medium mb-2">
                  Full Name *
                </label>
                <InputText
                  id="fullName"
                  value={application.personalInfo.fullName}
                  onChange={(e) => handlePersonalInfoChange("fullName", e.target.value)}
                  className="w-full"
                  required
                />
              </div>
              <div className="col-12 md:col-6">
                <label htmlFor="email" className="block text-900 font-medium mb-2">
                  Email *
                </label>
                <InputText
                  id="email"
                  type="email"
                  value={application.personalInfo.email}
                  onChange={(e) => handlePersonalInfoChange("email", e.target.value)}
                  className="w-full"
                  required
                />
              </div>
              <div className="col-12 md:col-6">
                <label htmlFor="phone" className="block text-900 font-medium mb-2">
                  Phone *
                </label>
                <InputText
                  id="phone"
                  value={application.personalInfo.phone}
                  onChange={(e) => handlePersonalInfoChange("phone", e.target.value)}
                  className="w-full"
                  required
                />
              </div>
              <div className="col-12 md:col-6">
                <label htmlFor="dob" className="block text-900 font-medium mb-2">
                  Date of Birth
                </label>
                <Calendar
                  id="dob"
                  value={application.personalInfo.dateOfBirth}
                  onChange={(e) => handlePersonalInfoChange("dateOfBirth", e.value)}
                  className="w-full"
                  showIcon
                />
              </div>
              <div className="col-12">
                <label htmlFor="address" className="block text-900 font-medium mb-2">
                  Address
                </label>
                <InputTextarea
                  id="address"
                  value={application.personalInfo.address}
                  onChange={(e) => handlePersonalInfoChange("address", e.target.value)}
                  rows={2}
                  className="w-full"
                />
              </div>
            </div>
          </Card>

          {/* Academic Information */}
          <Card title="Academic Background" className="mb-4">
            <div className="grid">
              <div className="col-12 md:col-6">
                <label htmlFor="education" className="block text-900 font-medium mb-2">
                  Highest Education Level *
                </label>
                <Dropdown
                  id="education"
                  value={application.academicInfo.previousEducation}
                  onChange={(e) => handleAcademicInfoChange("previousEducation", e.value)}
                  options={educationLevels}
                  placeholder="Select education level"
                  className="w-full"
                />
              </div>
              <div className="col-12 md:col-3">
                <label htmlFor="gpa" className="block text-900 font-medium mb-2">
                  GPA (if applicable)
                </label>
                <InputText
                  id="gpa"
                  value={application.academicInfo.gpa}
                  onChange={(e) => handleAcademicInfoChange("gpa", e.target.value)}
                  className="w-full"
                  placeholder="3.5"
                />
              </div>
              <div className="col-12 md:col-3">
                <label htmlFor="gradYear" className="block text-900 font-medium mb-2">
                  Graduation Year
                </label>
                <InputText
                  id="gradYear"
                  value={application.academicInfo.graduationYear}
                  onChange={(e) => handleAcademicInfoChange("graduationYear", e.target.value)}
                  className="w-full"
                  placeholder="2023"
                />
              </div>
            </div>
          </Card>

          {/* Course Preferences */}
          <Card title="Course Preferences" className="mb-4">
            <div className="grid">
              <div className="col-12 md:col-6">
                <label htmlFor="primary" className="block text-900 font-medium mb-2">
                  Primary Course Choice *
                </label>
                <Dropdown
                  id="primary"
                  value={application.coursePreferences.primaryChoice}
                  onChange={(e) => handleCoursePreferenceChange("primaryChoice", e.value)}
                  options={courseOptions}
                  placeholder="Select primary course"
                  className="w-full"
                />
              </div>
              <div className="col-12 md:col-6">
                <label htmlFor="secondary" className="block text-900 font-medium mb-2">
                  Secondary Course Choice
                </label>
                <Dropdown
                  id="secondary"
                  value={application.coursePreferences.secondaryChoice}
                  onChange={(e) => handleCoursePreferenceChange("secondaryChoice", e.value)}
                  options={courseOptions}
                  placeholder="Select secondary course"
                  className="w-full"
                />
              </div>
              <div className="col-12 md:col-6">
                <label htmlFor="startDate" className="block text-900 font-medium mb-2">
                  Preferred Start Date
                </label>
                <Calendar
                  id="startDate"
                  value={application.coursePreferences.startDate}
                  onChange={(e) => handleCoursePreferenceChange("startDate", e.value)}
                  className="w-full"
                  showIcon
                  minDate={new Date()}
                />
              </div>
            </div>
          </Card>

          {/* Documents Checklist */}
          <Card title="Required Documents" className="mb-4">
            <div className="grid">
              <div className="col-12">
                <p className="text-600 mb-3">
                  Please ensure you have the following documents ready for upload:
                </p>
              </div>
              <div className="col-12 md:col-4">
                <div className="flex align-items-center">
                  <Checkbox
                    inputId="transcript"
                    checked={application.documents.transcript}
                    onChange={(e) => handleDocumentChange("transcript", e.checked || false)}
                  />
                  <label htmlFor="transcript" className="ml-2">
                    Official Transcript
                  </label>
                </div>
              </div>
              <div className="col-12 md:col-4">
                <div className="flex align-items-center">
                  <Checkbox
                    inputId="recommendation"
                    checked={application.documents.recommendation}
                    onChange={(e) => handleDocumentChange("recommendation", e.checked || false)}
                  />
                  <label htmlFor="recommendation" className="ml-2">
                    Letter of Recommendation
                  </label>
                </div>
              </div>
              <div className="col-12 md:col-4">
                <div className="flex align-items-center">
                  <Checkbox
                    inputId="essay"
                    checked={application.documents.essay}
                    onChange={(e) => handleDocumentChange("essay", e.checked || false)}
                  />
                  <label htmlFor="essay" className="ml-2">
                    Personal Statement
                  </label>
                </div>
              </div>
            </div>
          </Card>

          {/* Personal Statement */}
          <Card title="Personal Statement" className="mb-4">
            <InputTextarea
              value={application.essay}
              onChange={(e) => setApplication(prev => ({ ...prev, essay: e.target.value }))}
              rows={6}
              className="w-full"
              placeholder="Tell us about your goals, interests, and why you want to pursue this course..."
            />
          </Card>

          {/* Submit Button */}
          <div className="flex gap-3">
            <Button
              label="Submit Application"
              icon="pi pi-send"
              className="p-button-success"
              onClick={submitApplication}
              disabled={!isFormValid()}
            />
            <Button
              label="Save Draft"
              icon="pi pi-save"
              className="p-button-outlined"
            />
          </div>
        </div>

        {/* Sidebar with helpful information */}
        <div className="col-12 lg:col-4">
          <Card title="Application Tips" className="mb-4">
            <ul className="list-none p-0 m-0">
              <li className="flex align-items-start mb-3">
                <i className="pi pi-check-circle text-green-500 mr-2 mt-1"></i>
                <span className="text-sm">Complete all required fields marked with *</span>
              </li>
              <li className="flex align-items-start mb-3">
                <i className="pi pi-check-circle text-green-500 mr-2 mt-1"></i>
                <span className="text-sm">Upload all required documents</span>
              </li>
              <li className="flex align-items-start mb-3">
                <i className="pi pi-check-circle text-green-500 mr-2 mt-1"></i>
                <span className="text-sm">Write a compelling personal statement</span>
              </li>
              <li className="flex align-items-start">
                <i className="pi pi-check-circle text-green-500 mr-2 mt-1"></i>
                <span className="text-sm">Review your application before submitting</span>
              </li>
            </ul>
          </Card>

          <Card title="Need Help?" className="mb-4">
            <p className="text-sm text-600 mb-3">
              If you have questions about the application process, contact our admissions office:
            </p>
            <div className="text-sm">
              <p><strong>Phone:</strong> (*************</p>
              <p><strong>Email:</strong> <EMAIL></p>
              <p><strong>Hours:</strong> Mon-Fri 8AM-5PM</p>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}
