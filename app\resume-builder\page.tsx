"use client";

import React, { useState } from "react";
import { Button } from "primereact/button";
import { InputText } from "primereact/inputtext";
import { InputTextarea } from "primereact/inputtextarea";
import { Card } from "primereact/card";
import { Divider } from "primereact/divider";
import { useRouter } from "next/navigation";
import Image from "next/image";
import Back from "../../public/Svg/back_icon.svg";

interface ResumeData {
  personalInfo: {
    fullName: string;
    email: string;
    phone: string;
    address: string;
  };
  summary: string;
  experience: Array<{
    id: string;
    company: string;
    position: string;
    duration: string;
    description: string;
  }>;
  education: Array<{
    id: string;
    institution: string;
    degree: string;
    year: string;
  }>;
  skills: string[];
}

export default function ResumeBuilder() {
  const router = useRouter();
  const [resumeData, setResumeData] = useState<ResumeData>({
    personalInfo: {
      fullName: "",
      email: "",
      phone: "",
      address: "",
    },
    summary: "",
    experience: [
      {
        id: "1",
        company: "",
        position: "",
        duration: "",
        description: "",
      },
    ],
    education: [
      {
        id: "1",
        institution: "",
        degree: "",
        year: "",
      },
    ],
    skills: [""],
  });

  const handlePersonalInfoChange = (field: string, value: string) => {
    setResumeData(prev => ({
      ...prev,
      personalInfo: {
        ...prev.personalInfo,
        [field]: value,
      },
    }));
  };

  const handleExperienceChange = (id: string, field: string, value: string) => {
    setResumeData(prev => ({
      ...prev,
      experience: prev.experience.map(exp =>
        exp.id === id ? { ...exp, [field]: value } : exp
      ),
    }));
  };

  const handleEducationChange = (id: string, field: string, value: string) => {
    setResumeData(prev => ({
      ...prev,
      education: prev.education.map(edu =>
        edu.id === id ? { ...edu, [field]: value } : edu
      ),
    }));
  };

  const addExperience = () => {
    const newId = Date.now().toString();
    setResumeData(prev => ({
      ...prev,
      experience: [
        ...prev.experience,
        {
          id: newId,
          company: "",
          position: "",
          duration: "",
          description: "",
        },
      ],
    }));
  };

  const addEducation = () => {
    const newId = Date.now().toString();
    setResumeData(prev => ({
      ...prev,
      education: [
        ...prev.education,
        {
          id: newId,
          institution: "",
          degree: "",
          year: "",
        },
      ],
    }));
  };

  const handleSkillChange = (index: number, value: string) => {
    setResumeData(prev => ({
      ...prev,
      skills: prev.skills.map((skill, i) => (i === index ? value : skill)),
    }));
  };

  const addSkill = () => {
    setResumeData(prev => ({
      ...prev,
      skills: [...prev.skills, ""],
    }));
  };

  const generateResume = async () => {
    try {
      const response = await fetch("/api/generate-resume", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(resumeData),
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.style.display = "none";
        a.href = url;
        a.download = "resume.pdf";
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
      } else {
        console.error("Failed to generate resume");
      }
    } catch (error) {
      console.error("Error generating resume:", error);
    }
  };

  return (
    <div className="p-4">
      {/* Header */}
      <div className="flex align-items-center gap-3 mb-4">
        <Button
          icon={<Image src={Back} alt="back" width={20} height={20} />}
          className="p-button-text"
          onClick={() => router.back()}
        />
        <h1 className="text-3xl font-bold m-0">Resume Builder</h1>
      </div>

      <div className="grid">
        <div className="col-12 lg:col-8">
          <Card title="Personal Information" className="mb-4">
            <div className="grid">
              <div className="col-12 md:col-6">
                <label htmlFor="fullName" className="block text-900 font-medium mb-2">
                  Full Name
                </label>
                <InputText
                  id="fullName"
                  value={resumeData.personalInfo.fullName}
                  onChange={(e) => handlePersonalInfoChange("fullName", e.target.value)}
                  className="w-full"
                />
              </div>
              <div className="col-12 md:col-6">
                <label htmlFor="email" className="block text-900 font-medium mb-2">
                  Email
                </label>
                <InputText
                  id="email"
                  value={resumeData.personalInfo.email}
                  onChange={(e) => handlePersonalInfoChange("email", e.target.value)}
                  className="w-full"
                />
              </div>
              <div className="col-12 md:col-6">
                <label htmlFor="phone" className="block text-900 font-medium mb-2">
                  Phone
                </label>
                <InputText
                  id="phone"
                  value={resumeData.personalInfo.phone}
                  onChange={(e) => handlePersonalInfoChange("phone", e.target.value)}
                  className="w-full"
                />
              </div>
              <div className="col-12 md:col-6">
                <label htmlFor="address" className="block text-900 font-medium mb-2">
                  Address
                </label>
                <InputText
                  id="address"
                  value={resumeData.personalInfo.address}
                  onChange={(e) => handlePersonalInfoChange("address", e.target.value)}
                  className="w-full"
                />
              </div>
            </div>
          </Card>

          <Card title="Professional Summary" className="mb-4">
            <InputTextarea
              value={resumeData.summary}
              onChange={(e) => setResumeData(prev => ({ ...prev, summary: e.target.value }))}
              rows={4}
              className="w-full"
              placeholder="Write a brief professional summary..."
            />
          </Card>

          <Card title="Work Experience" className="mb-4">
            {resumeData.experience.map((exp, index) => (
              <div key={exp.id} className="mb-4">
                <div className="grid">
                  <div className="col-12 md:col-6">
                    <label className="block text-900 font-medium mb-2">Company</label>
                    <InputText
                      value={exp.company}
                      onChange={(e) => handleExperienceChange(exp.id, "company", e.target.value)}
                      className="w-full"
                    />
                  </div>
                  <div className="col-12 md:col-6">
                    <label className="block text-900 font-medium mb-2">Position</label>
                    <InputText
                      value={exp.position}
                      onChange={(e) => handleExperienceChange(exp.id, "position", e.target.value)}
                      className="w-full"
                    />
                  </div>
                  <div className="col-12">
                    <label className="block text-900 font-medium mb-2">Duration</label>
                    <InputText
                      value={exp.duration}
                      onChange={(e) => handleExperienceChange(exp.id, "duration", e.target.value)}
                      className="w-full"
                      placeholder="e.g., Jan 2020 - Present"
                    />
                  </div>
                  <div className="col-12">
                    <label className="block text-900 font-medium mb-2">Description</label>
                    <InputTextarea
                      value={exp.description}
                      onChange={(e) => handleExperienceChange(exp.id, "description", e.target.value)}
                      rows={3}
                      className="w-full"
                    />
                  </div>
                </div>
                {index < resumeData.experience.length - 1 && <Divider />}
              </div>
            ))}
            <Button
              label="Add Experience"
              icon="pi pi-plus"
              className="p-button-outlined"
              onClick={addExperience}
            />
          </Card>

          <div className="flex gap-3">
            <Button
              label="Generate PDF Resume"
              icon="pi pi-download"
              className="p-button-success"
              onClick={generateResume}
            />
            <Button
              label="Save Draft"
              icon="pi pi-save"
              className="p-button-outlined"
            />
          </div>
        </div>
      </div>
    </div>
  );
}
